// Debug script to inspect Amazon product page DOM for image extraction
// Run this in the browser console on the Amazon product page

console.log('=== AMAZON IMAGE DOM INSPECTION ===');

// 1. Check for main landing image
const landingImage1 = document.querySelector('#landingImage');
console.log('1. Landing Image:', landingImage1);
if (landingImage1) {
  console.log('   - src:', landingImage1.src);
  console.log('   - data-old-hires:', landingImage1.getAttribute('data-old-hires'));
}

// 2. Check for alt images (thumbnails)
const altImages = document.querySelectorAll('#altImages img');
console.log('2. Alt Images (#altImages img):', altImages.length);
altImages.forEach((img, i) => {
  console.log(`   - Image ${i+1}:`, img.src);
});

// 3. Check for image block
const imageBlockImages = document.querySelectorAll('#imageBlock img');
console.log('3. Image Block (#imageBlock img):', imageBlockImages.length);
imageBlockImages.forEach((img, i) => {
  console.log(`   - Image ${i+1}:`, img.src);
});

// 4. Check for dynamic images
const dynamicImages = document.querySelectorAll('.a-dynamic-image');
console.log('4. Dynamic Images (.a-dynamic-image):', dynamicImages.length);
dynamicImages.forEach((img, i) => {
  console.log(`   - Image ${i+1}:`, img.src);
  console.log(`   - data-a-dynamic-image:`, img.getAttribute('data-a-dynamic-image'));
});

// 5. Check for thumbnail buttons
const thumbnailButtons = document.querySelectorAll('.a-button-thumbnail img');
console.log('5. Thumbnail Buttons (.a-button-thumbnail img):', thumbnailButtons.length);
thumbnailButtons.forEach((img, i) => {
  console.log(`   - Image ${i+1}:`, img.src);
});

// 6. Check for all Amazon media images
const allAmazonImages = document.querySelectorAll('img[src*="media-amazon.com"]');
console.log('6. All Amazon Media Images:', allAmazonImages.length);
allAmazonImages.forEach((img, i) => {
  if (i < 10) { // Show first 10 only
    console.log(`   - Image ${i+1}:`, img.src);
    console.log(`     - Parent:`, img.parentElement.tagName, img.parentElement.className);
  }
});

// 7. Check for image carousel container
const imageCarousel = document.querySelector('#imageBlock_feature_div');
console.log('7. Image Carousel Container:', imageCarousel);

// 8. Check for specific Amazon image gallery patterns
const galleryPatterns = [
  '#altImages',
  '#imageBlock', 
  '#imageBlockThumbs',
  '.a-button-thumbnail',
  '[data-a-dynamic-image]'
];

galleryPatterns.forEach(pattern => {
  const elements = document.querySelectorAll(pattern);
  console.log(`Pattern "${pattern}":`, elements.length, 'elements');
  if (elements.length > 0) {
    console.log('   - First element:', elements[0]);
  }
});

// 9. Look for data-a-dynamic-image attribute (contains image URLs)
const dynamicImageElements = document.querySelectorAll('[data-a-dynamic-image]');
console.log('9. Elements with data-a-dynamic-image:', dynamicImageElements.length);
dynamicImageElements.forEach((el, i) => {
  const dynamicData = el.getAttribute('data-a-dynamic-image');
  if (dynamicData) {
    try {
      const imageData = JSON.parse(dynamicData);
      console.log(`   - Element ${i+1} image URLs:`, Object.keys(imageData));
    } catch (e) {
      console.log(`   - Element ${i+1} raw data:`, dynamicData.substring(0, 100) + '...');
    }
  }
});

console.log('=== END INSPECTION ===');
