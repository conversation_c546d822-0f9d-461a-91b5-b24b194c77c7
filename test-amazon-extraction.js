// Test script to debug Amazon image extraction
// Run this in the browser console on the Amazon product page

console.log('=== TESTING AMAZON IMAGE EXTRACTION ===');

// Test the isMainProductImage function
function testIsMainProductImage(url) {
  if (!url || !url.includes('media-amazon.com')) {
    return false;
  }

  // Main product images follow specific URL patterns (relaxed to catch more valid images)
  const mainImagePatterns = [
    /\._AC_S[LY]\d{2,4}_/,        // _AC_SL1500_, _AC_SY879_ (2-4 digits)
    /\._AC_U[LXY]\d{2,4}_/,       // _AC_UL1500_, _AC_UX679_
    /\._S[LXY]\d{2,4}_/,          // _SL1500_, _SX679_
    /\._AC_\w{2}\d{2,4}_/,        // Any _AC_XXnnn_ with 2-4 digits
    /\._CR\d+,\d+,\d+,\d+_/       // Crop format _CR0,0,1000,1000_
  ];

  const hasMainPattern = mainImagePatterns.some(pattern => pattern.test(url));
  if (!hasMainPattern) {
    console.log(`❌ Rejected image (no main pattern): ${url}`);
    return false;
  }

  // Exclude non-product images (more selective exclusions)
  const excludePatterns = [
    /avatar|seller.*icon|badge(?!.*product)|logo(?!.*product)|button|star|prime|flag/i,
    /rating|review|brand(?!.*main)|warranty|delivery/i,
    /\.gif$/i,                        // Exclude GIFs (usually badges/icons)
    /sponsored|ad-|advertisement/i,   // Exclude ad images
    /thumb.*nail/i,                   // Exclude if specifically labeled as thumbnail
    /icon/i                           // Exclude icons
  ];

  const hasExcludePattern = excludePatterns.some(pattern => pattern.test(url));
  if (hasExcludePattern) {
    console.log(`❌ Rejected image (exclude pattern): ${url}`);
    return false;
  }

  // Relaxed size requirement (at least 100px instead of 200px)
  const sizeMatch = url.match(/[_\.][SU][LXY](\d+)[_\.]/);
  if (sizeMatch) {
    const size = parseInt(sizeMatch[1]);
    if (size < 100) {
      console.log(`❌ Rejected image (too small ${size}px): ${url}`);
      return false;
    }
  }

  console.log(`✅ Accepted image: ${url}`);
  return true;
}

// Test 1: Check for data-a-dynamic-image elements
console.log('\n1. Testing data-a-dynamic-image elements:');
const dynamicElements = document.querySelectorAll('[data-a-dynamic-image]');
console.log(`Found ${dynamicElements.length} elements with data-a-dynamic-image`);

dynamicElements.forEach((element, index) => {
  const dynamicData = element.getAttribute('data-a-dynamic-image');
  console.log(`Element ${index + 1}:`, element);
  
  if (dynamicData) {
    try {
      const imageData = JSON.parse(dynamicData);
      const imageUrls = Object.keys(imageData);
      console.log(`  - Contains ${imageUrls.length} image URLs:`);
      
      imageUrls.forEach((url, urlIndex) => {
        console.log(`    ${urlIndex + 1}. ${url}`);
        console.log(`       Valid: ${testIsMainProductImage(url)}`);
      });
    } catch (e) {
      console.log(`  - Error parsing JSON: ${e.message}`);
      console.log(`  - Raw data: ${dynamicData.substring(0, 200)}...`);
    }
  }
});

// Test 2: Check standard selectors
console.log('\n2. Testing standard selectors:');
const selectors = [
  '#altImages img',
  '#landingImage', 
  '.a-dynamic-image',
  '#imageBlock img',
  '.image-wrapper img',
  '#imageBlockThumbs img',
  '.a-button-thumbnail img'
];

selectors.forEach(selector => {
  const elements = document.querySelectorAll(selector);
  console.log(`${selector}: ${elements.length} elements`);
  
  elements.forEach((element, index) => {
    const url = element.src || element.getAttribute('data-src') || element.getAttribute('data-lazy-src') || element.getAttribute('data-old-hires');
    console.log(`  ${index + 1}. ${url}`);
    if (url) {
      console.log(`     Valid: ${testIsMainProductImage(url)}`);
    }
  });
});

// Test 3: Check all Amazon media images
console.log('\n3. All Amazon media images on page:');
const allAmazonImages = document.querySelectorAll('img[src*="media-amazon.com"]');
console.log(`Found ${allAmazonImages.length} total Amazon media images`);

const validImages = [];
allAmazonImages.forEach((img, index) => {
  const url = img.src;
  const isValid = testIsMainProductImage(url);
  
  if (isValid) {
    validImages.push(url);
  }
  
  if (index < 10) { // Show first 10 only
    console.log(`${index + 1}. ${url} - Valid: ${isValid}`);
  }
});

console.log(`\n✅ Total valid product images found: ${validImages.length}`);
validImages.forEach((url, index) => {
  console.log(`${index + 1}. ${url}`);
});

console.log('\n=== END TEST ===');
